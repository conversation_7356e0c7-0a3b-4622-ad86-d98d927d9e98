import { useEffect, useState } from 'react';
import DataTable from 'react-data-table-component';
import './App.css';



function App() {
  const [connected, setConnected] = useState(false);
  const [stockRows, setStockRows] = useState([]);
  const [columns, setColumns] = useState([]);
  const [messages, setMessages] = useState([]);
  const [selectedExchange, setSelectedExchange] = useState([]);

  useEffect(() => {
    const socket = new WebSocket('ws://localhost:8080');

    socket.onopen = () => {
      console.log('Connected to WebSocket');
      setConnected(true);

      const msgSend = {
        action: 'subscribe',
        exchange: selectedExchange,
      };
      socket.send(JSON.stringify(msgSend));
    };

  socket.onmessage = (event) => {
  console.log('Received:', event.data);
  setMessages(prev=>[...prev.slice(-49), event.data]);

  let dataPart = '';

  if (event.data.startsWith('Received:')) {
    dataPart = event.data.slice(9).trim();
  } else if (event.data.startsWith('{') || event.data.startsWith('[')) {
    dataPart = event.data.trim();
  } else {
    return;
  }

  if (!dataPart) return;

  let parsed = null;
  try {
    parsed = JSON.parse(dataPart);
  } catch (err) {
    console.error("JSON parsing error:", err);
    return;
  }

  const parsedArray = Array.isArray(parsed) ? parsed : [parsed];

  setStockRows((prevRows) => {
    const updatedRows = [...prevRows];

    parsedArray.forEach((stockObj) => {
      const filtered = Object.fromEntries(
        Object.entries(stockObj).filter(([, value]) => value !== null && value !== undefined)
      );

      const stockCode = stockObj.stockCode || "UNKNOWN";
      const index = updatedRows.findIndex(row => row.stockCode === stockCode);
      const prevRow = index !== -1 ? updatedRows[index] : null;

      const newRow = {
        stockCode,
        ...filtered,
        _changes: {},
        _highlight: {},
      };

      if (prevRow) {
        Object.keys(filtered).forEach((key) => {
          const oldVal = prevRow[key];
          const newVal = filtered[key];
          if (typeof newVal === 'number' && typeof oldVal === 'number') {
            if (newVal > oldVal) {
              newRow._highlight[key] = '#e6ffe6'; // xanh
            } else if (newVal < oldVal) {
              newRow._highlight[key] = '#ffe6e6'; // đỏ
            }
          }
        });
        updatedRows[index] = {
          ...prevRow,
          ...newRow,
        };
      } else {
        updatedRows.push(newRow);
      }

      // Reset background sau 1.5s
      setTimeout(() => {
        setStockRows((rows) =>
          rows.map((row) =>
            row.stockCode === stockCode
              ? {
                  ...row,
                  _highlight: {}, // reset về trắng
                }
              : row
          )
        );
      }, 1500);
    });

    // Cập nhật cột nếu chưa có
    if (updatedRows.length > 0 && columns.length === 0) {
      const sampleRow = updatedRows[0];
      const generatedColumns = Object.keys(sampleRow)
        .filter((key) => key !== "_highlight" && key !== "_changes")
        .map((key) => ({
          name: key,
          selector: (row) => row[key],
          sortable: true,
          cell: (row) => {
            const bgColor = row._highlight?.[key] || 'white';
            return (
              <div style={{
                backgroundColor: bgColor,
                color: 'black',
                transition: 'background-color 0.3s ease',
              }}>
                {row[key]}
              </div>
            );
          },
        }));

      setColumns(generatedColumns);
    }

    return updatedRows;
  });
};

    socket.onclose = () => {
      console.log('WebSocket disconnected');
      setConnected(false);
    };

    socket.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

    return () => {
      socket.close();
    };
  }, []);

  return (
    <div style={{ padding: 20 }}>
      <h2>React WebSocket Client</h2>
      <p>Status: {connected ? '🟢 Connected' : '🔴 Disconnected'}</p>

      <div style={{marginBottom: '1rem'}}>
        <label>Chọn sàn:</label>
        <select value={selectedExchange} onChange={e=> setSelectedExchange(e.target.value)}>
          <option value="hose">HOSE</option>
          <option value="vn30">VN30</option>
          <option value="stx_g4">STX_G4</option>
          <option value="hnx">HNX</option>
          <option value="hnx30">HNX30</option>
          <option value="upcom">UPCOM</option>
          
        </select>
      </div>
      <h3>Receive message:</h3>
      <div style={{
                  border: '1px solid #ccc',
                  padding:'10px',
                  maxHeight:'300px',
                  overflowY:'scroll',
                  background:'#f9f9f9',
                  fontFamily:'monospace',
                  fontSize:'12px'}}>{messages.map((msg, index) => (
                      <div key={index}>{msg}</div>
                ))}</div>

      <h3>Live Stock Data</h3>
      <DataTable
        columns={columns}
        data={stockRows}
        pagination
        highlightOnHover
        responsive
        striped
        persistTableHead
        noHeader={false}
      />
    </div>
  );
}

export default App;